// 章节式研究 Prompt 工具函数

import { AgentId, AgentActionName } from '@/types/chapter-research';
import { removeJsonMarkdown } from '@/utils/text';

// 生成开场 AI Prompt
export function generateOpeningPrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  collectedInfoSection: string,
  historicalConsensus?: string[]
): string {
  // 历史共识 - 总是显示（不受开关控制）
  let historicalInfo = '';
  if (historicalConsensus && historicalConsensus.length > 0) {
    historicalInfo = `

历史达成的共识：
${historicalConsensus.map((consensus, index) => `第${index + 1}轮共识: ${consensus}`).join('\n')}`;
  }

  return `# **指令：章节研究开场协议**

用户原始查询：${userQuery}

当前章节目标：${chapterGoal}
当前章节下已达成的历史共识信息${historicalInfo}

已收集的信息：
${collectedInfoSection}

你需要分析上述目标，找出信息缺口。请按以下步骤思考：

1. **分解目标**：把"${chapterGoal}"分成3-5个具体方面
   - 简要概括用户原始查询，明确用户研究目标。根据章节目标明确当前章节的研究方向
   - 想想完成这个研究方向需要了解哪些方面
   - 每个方面都应该是具体的、可查找的

2. **检查现有信息**：看看已有信息覆盖了哪些方面，重点关注之前的历史共识信息以及收集的查询检索信息(如果有的话)，从而判断
   - 之前已经有了哪些已达成共识的信息？
   - 还有哪些方面信息之前可能没考虑到，或者由于优先级不高而未被关注到？

3. **找出缺口**：选择2-3个你认为目前最缺少信息的方面
   - 优先选择对目标最重要的
   - 确保是能在网上找到的信息

输出格式要求：
- 必须返回纯JSON格式，不要包含任何markdown标记
- 不要在响应前后添加类似 \`\`\`json 的标记
- 使用普通文本，避免特殊格式化符号

输出格式示例：
{
  "thought": "1. 目标解构: 用户的研究主题是[概括用户研究目标]。当前章节的研究方向是[章节目标]。我将目标分解为[列出方面]。2. 知识盘点: 历史共识信息已经覆盖了[说明情况]。3. 差距识别: [说明哪些最缺乏]。",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "1. [方面名称]: (理由: [为什么缺乏])。2. [方面名称]: (理由: [为什么缺乏])。"
    }
  }
}`;
}

// 生成高层级响应 Prompt
export function generateHighLevelResponsePrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  currentRoundDiscussions: string,
  collectedInfoSection: string,
  historicalConsensus?: string[]
): string {
  // 历史共识 - 总是显示（不受开关控制）
  let historicalInfo = '';
  if (historicalConsensus && historicalConsensus.length > 0) {
    historicalInfo = `

历史达成的共识：
${historicalConsensus.map((consensus, index) => `第${index + 1}轮共识: ${consensus}`).join('\n')}`;
  }

  return `# **指令：高层级提议评审**

用户原始查询：${userQuery}

当前章节目标：${chapterGoal}

伙伴的提议：${currentRoundDiscussions}
当前章节下已达成的历史共识信息${historicalInfo}

已收集的信息：
${collectedInfoSection}

你需要评审伙伴的提议。按以下步骤思考：

1. **先独立分析**：不看伙伴提议，只参考历史共识达成的共识信息以及收集的查询检索信息(如果有的话)，你自己认为哪些信息最缺乏？
   - 明确用户研究目标。基于当前章节目标分析
   - 列出你认为最重要的2-3个缺口

2. **再对比决策**：比较你的分析和伙伴的提议，并参考历史达成的共识信息
   - 如果基本一致（比如都提到了同样的重点），就同意
   - 如果有重大差异（比如你认为伙伴忽略了关键点），就提出你的方案

输出格式（必须是JSON, 不可包含任何markdown格式语法）：

如果同意：
{
  "thought": "1. 独立分析: 用户的研究主题是[概括用户研究目标]。当前章节的研究方向是[章节目标]。历史共识信息已覆盖[说明情况]，我认为最缺乏的是[列出你的想法]。2. 对比决策: 伙伴提议的是[总结伙伴观点]。我们的想法[说明是否一致]，所以我同意。",
  "action": {
    "actionName": "HIGH_LEVEL_AGREE",
    "payload": {
      "agreement": "完整重复伙伴的提议"
    }
  }
}

如果不同意：
{
  "thought": "1. 独立分析: 用户的研究主题是[概括用户研究目标]。当前章节的研究方向是[章节目标]。历史共识信息已覆盖[说明情况]，我认为最缺乏的是[列出你的想法]。2. 对比决策: 伙伴提议的是[总结伙伴观点]。但我认为[说明差异]，所以提出修正。",
  "action": {
    "actionName": "HIGH_LEVEL_PROPOSE",
    "payload": {
      "proposal": "1. [方面名称]: (理由: [为什么重要])。2. [方面名称]: (理由: [为什么重要])。"
    }
  }
}`;
}

// 生成查询开始 Prompt
export function generateQueryStartPrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  highLevelConsensus: string,
  collectedInfoSection: string,
  historicalQueries?: Array<{query: string; researchGoal: string; fromRound: number}>
): string {
  // 历史查询 - 总是显示（不受开关控制）
  let historicalInfo = '';
  if (historicalQueries && historicalQueries.length > 0) {
    historicalInfo = `

历史查询记录：
${historicalQueries.map(hq => `第${hq.fromRound}轮: ${hq.query} (目的: ${hq.researchGoal})`).join('\n')}`;
  }

  return `# **指令：制定查询计划**

用户原始查询：${userQuery}

本轮已达成共识的研究方向：${highLevelConsensus}

章节目标：${chapterGoal}
当前章节下已搜集的历史查询记录${historicalInfo}

已收集的信息：
${collectedInfoSection}

你需要制定具体的搜索查询。按以下步骤思考：

1. **找出真正需要查的**：
   - 看看本轮共识中提到的每个方面
   - 检查历史查询记录和已有信息(如果有的话)是否已经回答了
   - 列出信息缺少或者尚未收集的具体问题

2. **设计搜索词**：
   - 每个问题用1-3个不同角度的搜索词
   - 搜索词要具体、容易找到结果
   - 注意比对历史查询记录，避免查询重复覆盖
   - 可以从这些角度搜索：
     * 直接查找：关键词 + 具体细节
     * 深入了解：关键词 + 原理/机制/分析
     * 多方视角：关键词 + 评价/比较/案例

输出格式（必须是JSON, 不能包含任何markdown格式语法）：
{
  "thought": "1. 知识盘点: 共识要研究[本轮已达成共识的研究方向]。检查已有信息，发现[说明哪些已知，哪些未知]。2. 查询设计: 针对[未知内容]，我设计了以下查询。",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "[具体的搜索词]",
          "researchGoal": "[这个查询要解决什么问题]"
        },
        {
          "query": "[具体的搜索词]",
          "researchGoal": "[这个查询要解决什么问题]"
        }
      ]
    }
  }
}

注意：
- 每个query都要对应一个具体的知识缺口
- researchGoal要描述清晰
- 避免过于宽泛的搜索词`;
}

// 生成查询响应 Prompt
export function generateQueryResponsePrompt(
  agentId: AgentId,
  chapterGoal: string,
  userQuery: string,
  highLevelConsensus: string,
  currentQueryDiscussions: string,
  collectedInfoSection: string,
  historicalQueries?: Array<{query: string; researchGoal: string; fromRound: number}>
): string {
  // 历史查询 - 总是显示（不受开关控制）
  let historicalInfo = '';
  if (historicalQueries && historicalQueries.length > 0) {
    historicalInfo = `

历史查询记录：
${historicalQueries.map(hq => `第${hq.fromRound}轮: ${hq.query} (目的: ${hq.researchGoal})`).join('\n')}`;
  }

  return `# **指令：战术查询审核协议**

用户原始查询：${userQuery}

本轮已达成共识的研究方向：${highLevelConsensus}

章节目标：${chapterGoal}

伙伴的查询提议：${currentQueryDiscussions}
当前章节下已搜集的历史查询记录：${historicalInfo}

已收集的信息：
${collectedInfoSection}

你需要审核伙伴的查询提议。按以下步骤思考：

1. **先想想你会查什么**：
   - 不看伙伴的提议
   - 基于共识方向和历史查询记录以及已有信息(如果有的话)，你觉得最该查什么，符合共识方向且避免重复查询？
   - 列出你认为最重要的2-10个查询

2. **再看伙伴的提议**：
   - 伙伴的查询是否符合我们的共识方向？
   - 查询词是否具体、容易找到结果？
   - 是否遗漏了重要问题？

3. **做出决定**：
   - 如果伙伴的查询很好，就执行
   - 如果有问题，就提出你的查询方案

输出格式（必须是JSON，不可包含任何markdown格式语法）：

如果同意执行：
{
  "thought": "1. 我的想法: 共识要研究[本轮已达成共识的研究方向]，基于历史查询记录和已有信息(如果有的话)，我认为应该查[你的想法]。2. 评估伙伴提议: 伙伴提议查[总结伙伴提议]。这些查询[说明为什么好]。3. 决定: 伙伴的提议很全面，可以执行。",
  "action": {
    "actionName": "EXECUTE_QUERIES",
    "payload": {
      "queries": [
        {
          "query": "[重复伙伴提议的具体搜索词]",
          "researchGoal": "[重复伙伴提议要解决的问题]"
        }
      ]
    }
  }
}

如果要修改：
{
  "thought": "1. 我的想法: 共识要研究[本轮已达成共识的研究方向]，基于历史查询记录和已有信息(如果有的话)，我认为应该查[你的想法]。2. 评估伙伴提议: 伙伴提议查[总结伙伴提议]。但[说明问题]。3. 决定: 需要调整查询方案。",
  "action": {
    "actionName": "QUERY_PROPOSE",
    "payload": {
      "queries": [
        {
          "query": "[你的搜索词]",
          "researchGoal": "[要解决什么问题]"
        }
      ],
      "rationale": "[简单说明为什么你的方案更好]"
    }
  }
}`;
}

// 根据动作类型和轮数生成对应的 Prompt
export function generateAgentPrompt(
  agentId: AgentId,
  actionName: AgentActionName,
  chapterGoal: string,
  userQuery: string,
  contextData: {
    collectedInfoSection: string;  // 直接传递处理好的内容
    currentRoundDiscussions?: string;
    highLevelConsensus?: string;
    currentQueryDiscussions?: string;
    // 历史数据字段
    historicalConsensus?: string[];
    historicalQueries?: Array<{query: string; researchGoal: string; fromRound: number}>;
  }
): string {
  const { 
    collectedInfoSection,
    currentRoundDiscussions = '',
    highLevelConsensus = '',
    currentQueryDiscussions = '',
    historicalConsensus,
    historicalQueries
  } = contextData;

  switch (actionName) {
    case 'HIGH_LEVEL_PROPOSE':
      if (!currentRoundDiscussions) {
        // 开场提议
        return generateOpeningPrompt(agentId, chapterGoal, userQuery, collectedInfoSection, historicalConsensus);
      } else {
        // 高层级响应中的提议
        return generateHighLevelResponsePrompt(agentId, chapterGoal, userQuery, currentRoundDiscussions, collectedInfoSection, historicalConsensus);
      }
      
    case 'HIGH_LEVEL_AGREE':
      return generateHighLevelResponsePrompt(agentId, chapterGoal, userQuery, currentRoundDiscussions, collectedInfoSection, historicalConsensus);
      
    case 'QUERY_PROPOSE':
      if (!currentQueryDiscussions) {
        // 查询开始
        return generateQueryStartPrompt(agentId, chapterGoal, userQuery, highLevelConsensus, collectedInfoSection, historicalQueries);
      } else {
        // 查询响应中的提议
        return generateQueryResponsePrompt(agentId, chapterGoal, userQuery, highLevelConsensus, currentQueryDiscussions, collectedInfoSection, historicalQueries);
      }
      
    case 'EXECUTE_QUERIES':
      return generateQueryResponsePrompt(agentId, chapterGoal, userQuery, highLevelConsensus, currentQueryDiscussions, collectedInfoSection, historicalQueries);
      
    default:
      throw new Error(`未知的动作类型: ${actionName}`);
  }
}

// 解析 AI 响应为结构化数据
export function parseAgentResponse(response: string): {
  thought: string;
  action: {
    actionName: AgentActionName;
    payload: any;
  };
} {
  try {
    // 检查响应是否为空
    if (!response || response.trim().length === 0) {
      throw new Error('AI响应为空');
    }

    let cleanedResponse = removeJsonMarkdown(response.trim());
    
    // 再次检查清理后的响应是否为空
    if (!cleanedResponse) {
      throw new Error('清理后的AI响应为空');
    }
    
    // 尝试解析JSON，如果失败则进行更激进的修复
    let parsed;
    try {
      parsed = JSON.parse(cleanedResponse);
    } catch (parseError) {
      console.warn('❌ [Prompt解析] 初次JSON解析失败，尝试修复:', parseError);
      
      // 更激进的JSON修复策略
      cleanedResponse = repairJsonString(cleanedResponse);
      
      try {
        parsed = JSON.parse(cleanedResponse);
        console.log('✅ [Prompt解析] JSON修复成功');
      } catch (secondParseError) {
        console.error('❌ [Prompt解析] JSON修复后仍然解析失败:', secondParseError);
        console.error('❌ [Prompt解析] 修复后的响应:', cleanedResponse);
        throw parseError; // 抛出原始解析错误
      }
    }
    
    if (!parsed.thought || !parsed.action || !parsed.action.actionName) {
      throw new Error('响应格式不完整');
    }
    
    return {
      thought: parsed.thought,
      action: {
        actionName: parsed.action.actionName as AgentActionName,
        payload: parsed.action.payload || {}
      }
    };
  } catch (error) {
    console.error('❌ [Prompt解析] 解析AI响应失败:', error);
    console.error('❌ [Prompt解析] 原始响应:', response.substring(0, 1000) + (response.length > 1000 ? '...' : ''));
    throw new Error(`无法解析AI响应: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 更激进的JSON字符串修复
function repairJsonString(jsonStr: string): string {
  // 移除所有控制字符
  jsonStr = jsonStr.replace(/[\x00-\x1F\x7F]/g, '');
  
  // 修复常见的JSON问题
  jsonStr = jsonStr
    // 修复字符串中未转义的引号 - 更精确的匹配
    .replace(/"([^"]*)"([^"]*)"([^"]*)"/g, (match, p1, p2, p3) => {
      // 如果中间部分看起来像是字符串内容而不是JSON结构
      if (!p2.includes(':') && !p2.includes('{') && !p2.includes('[')) {
        return `"${p1}\\"${p2}\\"${p3}"`;
      }
      return match;
    })
    // 修复可能的换行符问题
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    // 修复可能的制表符问题
    .replace(/\t/g, '\\t');
  
  return jsonStr;
}