import { z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";
import {
  systemInstruction,
  systemQuestionPrompt,
  reportPlanPrompt,
  serpQueriesPrompt,
  queryResultPrompt,
  citationRulesPrompt,
  searchResultPrompt,
  searchKnowledgeResultPrompt,
  reviewPrompt,
  finalReportCitationImagePrompt,
  finalReportReferencesPrompt,
  finalReportPrompt,
} from "@/constants/prompts";

export function getSERPQuerySchema() {
  return z
    .array(
      z
        .object({
          query: z.string().describe("The SERP query (查询词)."),
          language: z.enum(["chinese", "english"]).describe("Language of the query (查询语言)."),
          groupId: z.string().optional().describe("Optional group ID to associate related queries (可选的组ID用于关联相关查询)."),
          researchGoal: z
            .string()
            .describe(
              "First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions. JSON reserved words should be escaped."
            ),
        })
        .required({ query: true, language: true, researchGoal: true })
    )
    .describe(`List of SERP queries with language specification.`);
}

export function getSERPQueryOutputSchema() {
  const SERPQuerySchema = getSERPQuerySchema();
  return JSON.stringify(zodToJsonSchema(SERPQuerySchema), null, 4);
}

export function getSystemPrompt() {
  return systemInstruction.replace("{now}", new Date().toISOString());
}

export function generateQuestionsPrompt(query: string) {
  return systemQuestionPrompt.replace("{query}", query);
}

export function writeReportPlanPrompt(query: string) {
  return reportPlanPrompt.replace("{query}", query);
}

export function generateSerpQueriesPrompt(plan: string, searchMode: SearchMode = "bilingual") {
  return serpQueriesPrompt
    .replace("{plan}", plan)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function processResultPrompt(query: string, researchGoal: string) {
  return queryResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal);
}

export function processSearchResultPrompt(
  query: string,
  researchGoal: string,
  results: Source[],
  enableReferences: boolean,
  globalIndexMap?: Map<string, number> // 新增：可选的全局编号映射
) {
  const context = results.map(
    (result, idx) => {
      // 如果提供了全局编号映射，使用全局编号；否则使用原来的 idx + 1
      const indexToUse = globalIndexMap?.get(result.url) ?? (idx + 1);
      return `<content index="${indexToUse}" url="${result.url}">\n${
        result.content
      }\n</content>`;
    }
  );
  return (
    searchResultPrompt + (enableReferences ? `\n\n${citationRulesPrompt}` : "")
  )
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function processSearchKnowledgeResultPrompt(
  query: string,
  researchGoal: string,
  results: Knowledge[]
) {
  const context = results.map(
    (result, idx) =>
      `<content index="${idx + 1}" url="${location.host}">\n${
        result.content
      }\n</content>`
  );
  return searchKnowledgeResultPrompt
    .replace("{query}", query)
    .replace("{researchGoal}", researchGoal)
    .replace("{context}", context.join("\n"));
}

export function reviewSerpQueriesPrompt(
  plan: string,
  learning: string[],
  suggestion: string,
  searchMode: SearchMode = "bilingual"
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  return reviewPrompt
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{suggestion}", suggestion)
    .replace("{searchMode}", searchMode)
    .replace("{outputSchema}", getSERPQueryOutputSchema());
}

export function writeFinalReportPrompt(
  plan: string,
  learning: string[],
  source: Source[],
  images: ImageSource[],
  requirement: string,
  enableCitationImage: boolean,
  enableReferences: boolean
) {
  const learnings = learning.map(
    (detail) => `<learning>\n${detail}\n</learning>`
  );
  const sources = source.map(
    (item, idx) =>
      `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`
  );
  const imageList = images.map(
    (source, idx) => `${idx + 1}. ![${source.description}](${source.url})\n   (图片说明：${source.chineseDescription || source.description})`
  );
  return (
    finalReportPrompt +
    (enableCitationImage
      ? `\n**Including meaningful images from the previous research in the report is very helpful.**\n\n${finalReportCitationImagePrompt}`
      : "") +
    (enableReferences ? `\n\n${finalReportReferencesPrompt}` : "")
  )
    .replace("{plan}", plan)
    .replace("{learnings}", learnings.join("\n"))
    .replace("{sources}", sources.join("\n"))
    .replace("{images}", imageList.join("\n"))
    .replace("{requirement}", requirement);
}

// Adversarial plan optimization related Schema and Prompt functions
export const planTurnSchema = {
  type: 'object',
  properties: {
    thought: { 
      type: 'string', 
      description: "Your thought process. If you are the architect, explain why you designed the plan this way. If you are the critic, explain why you raised these critical opinions." 
    },
    action: { 
      type: 'string', 
      enum: ['propose_plan', 'critique_plan', 'finish_discussion'],
      description: "Your action: propose_plan (architect proposes or modifies plan), critique_plan (critic provides feedback), finish_discussion (critic ends discussion and finalizes the plan)."
    },
    plan: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          section_title: { type: 'string' },
          summary: { type: 'string', description: "Summarize the core content of this section in one sentence." }
        },
        required: ['section_title', 'summary']
      },
      nullable: true,
      description: "Report plan. Only provide when action is 'propose_plan' or 'finish_discussion'."
    },
    critique: {
      oneOf: [
        { type: 'string' },
        {
          type: 'object',
          properties: {
            topic_relevance: {
              type: 'array',
              items: { type: 'string' }
            },
            completeness: {
              type: 'array', 
              items: { type: 'string' }
            },
            non_redundancy: {
              type: 'array',
              items: { type: 'string' }
            },
            conciseness: {
              type: 'array',
              items: { type: 'string' }
            },
            abstraction_guidance: {
              type: 'array',
              items: { type: 'string' }
            },
            logical_flow: {
              type: 'array',
              items: { type: 'string' }
            }
          }
        }
      ],
      nullable: true,
      description: "REQUIRED when action is 'critique_plan': Specific, actionable criticism and improvement suggestions. Can be either a string of feedback or a structured object with categorized critiques. MUST be null/empty for other actions."
    },
    finish_reason: {
      type: 'string',
      nullable: true,
      description: "Reason for finalizing the plan. Only provide when action is 'finish_discussion'."
    }
  },
  required: ['thought', 'action'],
  additionalProperties: false,
};

// Interface for adversarial mechanism prompt parameters
interface ReportPlanPromptParams {
  nextPersona: '架构师' | '批判者';
  query: string;
  conversationText: string;
  isFirstTurn: boolean;
  mode?: 'fixed' | 'auto';
  maxRounds?: number;
  currentRound?: number;
}

// Function to generate Architect prompt (specialized for plan creation)
// V3.1 — Architect (Title-Tagged 总‑分)
export const getArchitectPrompt = ({
  query,
  conversationText,
  isFirstTurn,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: Omit<ReportPlanPromptParams, 'nextPersona'>): string => `
You are an AI research assistant acting as the **Architect**. Your task is to create and refine high-quality report outlines.
Your response must be a single JSON object that follows the predefined schema, and use Chinese as outline language.

**Core Research Topic (from user):**
<QUERY>
${query}
</QUERY>

**Current Plan Discussion Process:**
${conversationText || "The conversation has not started yet."}

--- Architect Role & Responsibilities ---

**Your Role:** You are the **Architect**.

**Non-Creation & Isolation Policy:**
- **Synthesis-Only:** Do not propose new models/methods/benchmarks/frameworks. Report existing standards and viewpoints; flag gaps as “研究空白/待补充”.
- **Chapter Independence:** No cross-chapter references or dependencies; no internal benchmarks that constrain other chapters.

**Outline Shape (总‑分，仅前置总章，无终结章):**
- The outline MUST follow **总‑分** only:
  - **Chapter 1** = “总”（背景/引言/概念与范畴综述，基于既有定义与文献），**不得**为后文设定内部基准或约束。
  - **Chapters 2..N** = “分”（并列的独立维度），互不依赖、互不引用。
- **禁止**添加任何结尾性的“结论/总结/总评/建议/展望/统一框架/Roadmap”等章节。
- **标题标注要求**：\`section_title\` 必须在末尾带类型标签：
  - 第1章加 **【总】**
  - 第2..N章加 **【分】**
- **排序建议**：在不表达依赖关系的前提下，可按“概念→方法现状→应用/市场→政策/风险→其他维度”的阅读友好顺序排列，但各章保持平行关系。

**Output Format:** JSON with:
- \`thought\`: reasoning + a brief compliance note that you followed Synthesis-Only, Independence, and 总‑分 structure (one 总, multiple 分, no concluding chapter)
- \`action\`: "propose_plan"
- \`plan\`: Array of { \`section_title\`, \`summary\` }

**Summary Formatting Rules:**
- Must start with: “本章节为报告第X章。本章节的研究目标是：…”
- Must end with the independence guard sentence exactly:
  **“【独立性约束：本章节不依赖其他章节内容，不设自定义基准或方法，仅基于既有公开信息进行综述与陈述。】”**

**Language Guardrails:**
- Prefer: 综述/梳理/比较/评估/总结/界定（基于权威来源）/识别研究空白
- Avoid: 提出/设计/构建/发明/建立基准/本文方法/我们将/基于上一章/如前文所述/为后续设定…

${isFirstTurn ? `**First Round:** As this is the first round, your action must be 'propose_plan'.` : ''}

${mode === 'fixed' ? `**Fixed Mode:** This is round ${currentRound}/${maxRounds}. Continue until final round.` : ''}

**Output Requirements:**
- Valid JSON, Chinese outline language, plan-focused
- 4-5 main sections recommended
`;


// V3.1 — Critic (Title-Tagged 总‑分)
export const getCriticPrompt = ({
  query,
  conversationText,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: Omit<ReportPlanPromptParams, 'nextPersona' | 'isFirstTurn'>): string => `
You are an AI research assistant acting as the **Critic**. Your task is to rigorously evaluate report outlines and decide when they meet quality standards.
Your response must be a single JSON object that follows the predefined schema, and use Chinese as outline language.

**Core Research Topic (from user):**
<QUERY>
${query}
</QUERY>

**Current Plan Discussion Process:**
${conversationText || "The conversation has not started yet."}

--- Critic Role & Responsibilities ---

**Your Role:** You are the **Critic**.

**Output Format for Critique:**
- \`thought\`: evaluation reasoning-explain WHY you're proposing a critique
- \`action\`: "critique_plan"
- \`critique\`: concrete suggestions; **no** example formats/code/JSON

**Output Format for Finalization:**
- \`thought\`: final evaluation reasoning-explain WHY the plan is acceptable
- \`action\` = "finish_discussion",
- \`plan\`: unchanged from previous round.You MUST COPY exactly the Architect's plan content and structure.,which is an Array of { \`section_title\`, \`summary\` }
- \`finish_reason\`: reason for finalizing the plan shrotly

**Evaluation Criteria (hard checks for 总‑分):**
1. **Structure (总‑分 only):**
   - Exactly **one** chapter whose \`section_title\` ends with **【总】** and it must be Chapter 1.
   - All other chapters must end with **【分】**.
   - **No concluding chapter**: reject any section titled/behaving like “结论/总结/总评/建议/展望/统一框架/Roadmap/综合汇总”.
2. **Synthesis-Only:** No invented models/methods/benchmarks/frameworks.
3. **Chapter Independence:** No cross-chapter references or dependencies; no chapter sets internal benchmarks for others.
4. **Summary Format:** Each summary **starts** with “本章节为报告第X章。本章节的研究目标是：…”
5. **Guard Sentence Presence:** Each summary **ends** exactly with:
   “【独立性约束：本章节不依赖其他章节内容，不设自定义基准或方法，仅基于既有公开信息进行综述与陈述。】”
6. Topic relevance, completeness, non-redundancy, granularity, abstraction.

**Mandatory Rejection Triggers:**
- More/less than one **【总】**; any missing **【分】** on Chapters 2..N
- Any concluding/final/summary-like section
- Any phrases like “提出/设计/构建/发明/建立基准/本文方法/我们将/基于上一章/如前文所述/为后续设定…”
- Cross-chapter dependencies or internal benchmarks
- numnber of main sections less than 4 or greater than 5

${mode === 'fixed' ? (
  currentRound === maxRounds 
    ? `**Fixed Mode - FINAL ROUND:** This is the FINAL round (${currentRound}/${maxRounds}). You MUST use 'finish_discussion' action to conclude the optimization process. You cannot use 'critique_plan' in this round.`
    : `**Fixed Mode:** This is round ${currentRound}/${maxRounds}. Continue critiquing until the final round ${maxRounds}, where you must use 'finish_discussion'.`
) : ''}

**Critical Requirements:**
- 'critique_plan' = specific, actionable feedback
- 'finish_discussion' only when all hard checks pass
- Valid JSON
`;


// Function to generate adversarial mechanism prompts (legacy compatibility wrapper)
export const getReportPlanPrompt = ({
  nextPersona,
  query,
  conversationText,
  isFirstTurn,
  mode = 'auto',
  maxRounds = 5,
  currentRound = 1
}: ReportPlanPromptParams): string => {
  if (nextPersona === '架构师') {
    return getArchitectPrompt({
      query,
      conversationText,
      isFirstTurn,
      mode,
      maxRounds,
      currentRound
    });
  } else {
    return getCriticPrompt({
      query,
      conversationText,
      mode,
      maxRounds,
      currentRound
    });
  }
};
