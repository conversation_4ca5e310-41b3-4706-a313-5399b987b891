import React, { useState } from 'react';
import { 
  FileTex<PERSON>, 
  Play, 
  Loader2, 
  CheckCircle, 
  Clock,
  AlertCircle,
  BookOpen,
  List,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useChapterResearch } from '@/hooks/useChapterResearch';
import { useTaskStore } from '@/store/task';
import { ChapterStatus } from '@/types/chapter-research';

interface ChapterReportControllerProps {
  className?: string;
}

export function ChapterReportController({ className }: ChapterReportControllerProps) {
  const taskStore = useTaskStore();
  const { 
    chapterResearch, 
    generateChapterReport, 
    generateAllChapterReports, 
    generateFinalReport 
  } = useChapterResearch();
  
  const [isGeneratingAll, setIsGeneratingAll] = useState(false);
  const [isGeneratingFinal, setIsGeneratingFinal] = useState(false);
  const [generatingChapter, setGeneratingChapter] = useState<string | null>(null);

  // 统计信息
  const completedChapters = chapterResearch.chapters.filter(c => c.status === ChapterStatus.COMPLETED);
  const existingReports = chapterResearch.chapterReports;
  const unreportedChapters = completedChapters.filter(chapter => 
    !existingReports.some(report => report.chapterId === chapter.id)
  );

  const canGenerateReports = completedChapters.length > 0;
  const canGenerateFinalReport = existingReports.length > 0;
  const allChaptersReported = unreportedChapters.length === 0 && completedChapters.length > 0;

  // 生成单个章节报告
  const handleGenerateChapterReport = async (chapterId: string) => {
    setGeneratingChapter(chapterId);
    try {
      await generateChapterReport(chapterId);
    } catch (error) {
      console.error('生成章节报告失败:', error);
    } finally {
      setGeneratingChapter(null);
    }
  };

  // 生成所有章节报告
  const handleGenerateAllReports = async () => {
    setIsGeneratingAll(true);
    try {
      await generateAllChapterReports();
    } catch (error) {
      console.error('批量生成报告失败:', error);
    } finally {
      setIsGeneratingAll(false);
    }
  };

  // 生成最终报告
  const handleGenerateFinalReport = async () => {
    setIsGeneratingFinal(true);
    try {
      await generateFinalReport();
    } catch (error) {
      console.error('生成最终报告失败:', error);
    } finally {
      setIsGeneratingFinal(false);
    }
  };

  if (!chapterResearch.isActive) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <FileText className="h-5 w-5 text-blue-600" />
          报告生成控制台
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 状态概览 */}
        <div className="grid grid-cols-3 gap-3 text-sm">
          <div className="text-center">
            <div className="text-xl font-semibold text-green-600">{completedChapters.length}</div>
            <div className="text-muted-foreground">信息收集完成</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-semibold text-blue-600">{existingReports.length}</div>
            <div className="text-muted-foreground">章节报告已生成</div>
          </div>
          <div className="text-center">
            <div className="text-xl font-semibold text-purple-600">
              {taskStore.finalReport ? 1 : 0}
            </div>
            <div className="text-muted-foreground">最终报告</div>
          </div>
        </div>

        <Separator />

        {/* 批量操作 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">批量操作</h4>
            {allChaptersReported && (
              <Badge variant="secondary" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                全部已生成
              </Badge>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleGenerateAllReports}
              disabled={!canGenerateReports || isGeneratingAll}
              size="sm"
              className="flex-1"
            >
              {isGeneratingAll ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <List className="h-4 w-4 mr-2" />
              )}
              生成所有章节报告
            </Button>

            <Button
              onClick={handleGenerateFinalReport}
              disabled={!canGenerateFinalReport || isGeneratingFinal}
              size="sm"
              variant="outline"
              className="flex-1"
            >
              {isGeneratingFinal ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <BookOpen className="h-4 w-4 mr-2" />
              )}
              生成最终报告
            </Button>
          </div>

          {unreportedChapters.length > 0 && (
            <div className="text-xs text-muted-foreground">
              <AlertCircle className="h-3 w-3 inline mr-1" />
              还有 {unreportedChapters.length} 个章节未生成报告
            </div>
          )}
        </div>

        {/* 单独章节控制 */}
        {completedChapters.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-medium text-sm">单独章节控制</h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {completedChapters.map(chapter => {
                  const hasReport = existingReports.some(r => r.chapterId === chapter.id);
                  const isGenerating = generatingChapter === chapter.id;
                  
                  return (
                    <div 
                      key={chapter.id}
                      className="flex items-center justify-between gap-2 p-2 bg-muted/30 rounded-md"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">
                          {chapter.title}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {hasReport ? (
                            <span className="text-green-600">
                              <CheckCircle className="h-3 w-3 inline mr-1" />
                              报告已生成
                            </span>
                          ) : (
                            <span className="text-orange-600">
                              <Clock className="h-3 w-3 inline mr-1" />
                              等待生成报告
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <Button
                        onClick={() => handleGenerateChapterReport(chapter.id)}
                        disabled={isGenerating}
                        size="sm"
                        variant={hasReport ? "outline" : "default"}
                        className="flex-shrink-0"
                      >
                        {isGenerating ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : hasReport ? (
                          <RefreshCw className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}

        {/* 提示信息 */}
        {completedChapters.length === 0 && (
          <div className="text-center py-6 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">暂无已完成的章节</p>
            <p className="text-xs mt-1">完成章节信息收集后即可生成报告</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}