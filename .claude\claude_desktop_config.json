{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@context7/mcp-server"], "env": {}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:\\Desktop\\ellchan\\deepresearch-cc"], "env": {}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": ""}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": ""}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {}}}}