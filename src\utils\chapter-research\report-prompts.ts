import { 
  citationRulesPrompt, 
  finalReportCitationImagePrompt, 
  finalReportReferencesPrompt 
} from '@/constants/prompts';
import { generateRefId } from '@/utils/reference-manager';
// Source 和 ImageSource 是全局类型，在 types.d.ts 中定义

export const generateChapterReportPrompt = (
  chapterTitle: string,
  chapterGoal: string,
  collectedInfo: string,
  sources: Source[],
  images: ImageSource[],
  enableReferences: boolean = true,
  enableImages: boolean = true,
): string => {
  // 构建结构化的sources部分（使用临时标识符）
  const sourcesSection = enableReferences && sources.length > 0
    ? sources.map((source) => {
        const refId = generateRefId(source.url);
        return `<source index="${refId}" url="${source.url}">\n${source.title}\n</source>`;
      }).join('\n')
    : '';
  
  const imageSection = enableImages && images.length > 0 
    ? `\n\n**可用图片资源:**\n${images.map(img => `- ![${img.description || ''}](${img.url})${img.chineseDescription ? ` (${img.chineseDescription})` : ''}`).join('\n')}\n\n${finalReportCitationImagePrompt}` 
    : '';
  
  const referencesSection = enableReferences && sources.length > 0 
    ? `\n\n${finalReportReferencesPrompt}` 
    : '';

  return `
基于以下信息，为"${chapterTitle}"章节写一份详细的研究报告：

章节目标: ${chapterGoal}

收集的信息:
${collectedInfo}

${enableReferences && sources.length > 0 ? `参考来源（请在报告中引用这些来源）:
<SOURCES>
${sourcesSection}
</SOURCES>` : ''}

要求：
1. 围绕章节目标组织内容
2. 使用收集到的信息支撑观点
3. 结构清晰，逻辑连贯，使用markdown格式，章节题目为##[章节题目]， 小标题为###[小标题]
4. 使用中文写作
5. 包含必要的引用说明${enableReferences ? '和引用列表' : ''}${enableImages ? '和相关图片' : ''}
${enableReferences ? '6. 必须引用提供的参考来源，使用提供的引用标识符格式，例如[REF_a1b2c3d4]' : ''}

${enableReferences ? citationRulesPrompt : ''}${imageSection}${referencesSection}

请生成一份完整内容丰富的章节报告。
  `;
};