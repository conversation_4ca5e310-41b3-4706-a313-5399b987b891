import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Download, FileText } from 'lucide-react';
import { useTaskStore } from '@/store/task';
import { ChapterReport } from '@/types/chapter-research';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// 图片Lightbox组件
const ImageLightbox: React.FC<{
  src: string;
  alt: string;
  isOpen: boolean;
  onClose: () => void;
}> = ({ src, alt, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div className="relative max-w-4xl max-h-full">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img 
          src={src} 
          alt={alt}
          className="max-w-full max-h-full object-contain"
          onClick={(e) => e.stopPropagation()}
        />
        <Button
          variant="secondary"
          size="sm"
          className="absolute top-2 right-2"
          onClick={onClose}
        >
          关闭
        </Button>
      </div>
    </div>
  );
};

// 自定义Markdown渲染器，支持图片lightbox
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [lightboxImage, setLightboxImage] = useState<{src: string, alt: string} | null>(null);

  const handleImageClick = (src: string, alt: string) => {
    setLightboxImage({ src, alt });
  };

  return (
    <>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          img: ({ src, alt }) => (
            // eslint-disable-next-line @next/next/no-img-element
            <img 
              src={src} 
              alt={alt || ''} 
              className="max-w-full h-auto cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => handleImageClick(typeof src === 'string' ? src : '', alt || '')}
            />
          )
        }}
      >
        {content}
      </ReactMarkdown>
      <ImageLightbox
        src={lightboxImage?.src || ''}
        alt={lightboxImage?.alt || ''}
        isOpen={!!lightboxImage}
        onClose={() => setLightboxImage(null)}
      />
    </>
  );
};

// 单个章节报告卡片组件
const ChapterReportCard: React.FC<{ 
  report: ChapterReport; 
  chapterIndex: number;
}> = ({ report, chapterIndex }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const handleExport = () => {
    const blob = new Blob([report.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="w-full">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CardHeader className="pb-3">
          <CollapsibleTrigger asChild>
            <div className="flex items-center justify-between cursor-pointer">
              <div className="flex items-center gap-2">
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                <CardTitle className="text-lg">
                  第{chapterIndex + 1}章：{report.title}
                </CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {report.sources.length} 个来源
                </Badge>
                <Badge variant="outline">
                  {report.images.length} 张图片
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleExport();
                  }}
                >
                  <Download className="h-4 w-4 mr-1" />
                  导出
                </Button>
              </div>
            </div>
          </CollapsibleTrigger>
        </CardHeader>
        <CollapsibleContent>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <MarkdownRenderer content={report.content} />
            </div>
            
            {report.sources.length > 0 && (
              <div className="mt-6 pt-4 border-t">
                <h4 className="font-medium text-sm text-muted-foreground mb-2">
                  参考来源 ({report.sources.length})
                </h4>
                <div className="space-y-1">
                  {report.sources.map((source, index) => (
                    <div key={index} className="text-xs text-muted-foreground">
                      [{index + 1}]: 
                      <a 
                        href={source.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        {source.title || source.url}
                      </a>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

// 主要的章节报告查看器组件
export const ChapterReportsViewer: React.FC = () => {
  const taskStore = useTaskStore();
  const { chapterResearch } = taskStore;

  // 按章节顺序排序报告
  const sortedReports = useMemo(() => {
    return chapterResearch.chapterReports
      .slice()
      .sort((a, b) => {
        const aIndex = chapterResearch.chapters.findIndex(c => c.id === a.chapterId);
        const bIndex = chapterResearch.chapters.findIndex(c => c.id === b.chapterId);
        return aIndex - bIndex;
      });
  }, [chapterResearch.chapterReports, chapterResearch.chapters]);

  const handleExportAll = () => {
    const combinedContent = sortedReports
      .map((report, index) => `# 第${index + 1}章：${report.title}\n\n${report.content}`)
      .join('\n\n---\n\n');

    const blob = new Blob([combinedContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '章节报告汇总.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (sortedReports.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>暂无章节报告</p>
            <p className="text-sm mt-1">完成章节信息收集后，可生成章节报告</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">章节报告</h3>
          <p className="text-sm text-muted-foreground">
            共 {sortedReports.length} 个章节报告
          </p>
        </div>
        <Button onClick={handleExportAll} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          导出全部
        </Button>
      </div>
      
      <div className="space-y-3">
        {sortedReports.map((report, index) => (
          <ChapterReportCard
            key={report.chapterId}
            report={report}
            chapterIndex={index}
          />
        ))}
      </div>
    </div>
  );
};

export default ChapterReportsViewer;